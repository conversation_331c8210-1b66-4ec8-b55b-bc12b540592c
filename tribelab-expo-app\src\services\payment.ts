import { Platform } from 'react-native';
import * as SecureStore from 'expo-secure-store';
import { paymentAPI } from './api';

// Payment gateway types
export type PaymentGateway = 'razorpay' | 'stripe';

export interface PaymentOptions {
  amount: number;
  currency: string;
  description: string;
  orderId?: string;
  customerId?: string;
  metadata?: any;
}

export interface PaymentResult {
  success: boolean;
  paymentId?: string;
  orderId?: string;
  signature?: string;
  error?: string;
}

export interface SubscriptionOptions {
  planId: string;
  customerId?: string;
  paymentMethodId?: string;
  trialDays?: number;
}

class PaymentService {
  private gateway: PaymentGateway = 'razorpay'; // Default gateway
  private razorpayKey: string = 'rzp_test_your_key_here'; // Replace with actual key

  constructor() {
    this.initializeGateway();
  }

  private async initializeGateway() {
    try {
      // Get stored payment gateway preference
      const storedGateway = await SecureStore.getItemAsync('payment_gateway');
      if (storedGateway) {
        this.gateway = storedGateway as PaymentGateway;
      }
    } catch (error) {
      console.error('Failed to initialize payment gateway:', error);
    }
  }

  async setPaymentGateway(gateway: PaymentGateway) {
    this.gateway = gateway;
    try {
      await SecureStore.setItemAsync('payment_gateway', gateway);
    } catch (error) {
      console.error('Failed to store payment gateway preference:', error);
    }
  }

  async processPayment(options: PaymentOptions): Promise<PaymentResult> {
    try {
      // Create order on backend first
      const orderResponse = await paymentAPI.processPayment({
        amount: options.amount,
        currency: options.currency,
        type: 'one_time',
        metadata: options.metadata,
      });

      const { orderId, amount, currency } = orderResponse.data;

      if (this.gateway === 'razorpay') {
        return await this.processRazorpayPayment({
          ...options,
          orderId,
          amount,
          currency,
        });
      } else {
        return await this.processStripePayment({
          ...options,
          orderId,
          amount,
          currency,
        });
      }
    } catch (error: any) {
      console.error('Payment processing failed:', error);
      return {
        success: false,
        error: error.message || 'Payment failed',
      };
    }
  }

  private async processRazorpayPayment(options: PaymentOptions & { orderId: string }): Promise<PaymentResult> {
    try {
      // For React Native, we would use react-native-razorpay
      // For Expo, we need to use WebView or redirect to web payment
      
      if (Platform.OS === 'web') {
        // Web implementation using Razorpay Checkout
        return new Promise((resolve) => {
          const script = document.createElement('script');
          script.src = 'https://checkout.razorpay.com/v1/checkout.js';
          script.onload = () => {
            const rzp = new (window as any).Razorpay({
              key: this.razorpayKey,
              amount: options.amount * 100, // Convert to paise
              currency: options.currency,
              order_id: options.orderId,
              name: 'TribeLab',
              description: options.description,
              handler: (response: any) => {
                resolve({
                  success: true,
                  paymentId: response.razorpay_payment_id,
                  orderId: response.razorpay_order_id,
                  signature: response.razorpay_signature,
                });
              },
              modal: {
                ondismiss: () => {
                  resolve({
                    success: false,
                    error: 'Payment cancelled by user',
                  });
                },
              },
            });
            rzp.open();
          };
          document.head.appendChild(script);
        });
      } else {
        // For mobile, redirect to payment URL or use WebView
        // This is a simplified implementation
        return {
          success: false,
          error: 'Mobile payment not implemented yet',
        };
      }
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Razorpay payment failed',
      };
    }
  }

  private async processStripePayment(options: PaymentOptions & { orderId: string }): Promise<PaymentResult> {
    try {
      // Stripe implementation would go here
      // For now, return a mock response
      return {
        success: false,
        error: 'Stripe payment not implemented yet',
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Stripe payment failed',
      };
    }
  }

  async createSubscription(options: SubscriptionOptions): Promise<PaymentResult> {
    try {
      const response = await paymentAPI.subscribe(options.planId, options.paymentMethodId);
      
      return {
        success: true,
        paymentId: response.data.subscriptionId,
      };
    } catch (error: any) {
      console.error('Subscription creation failed:', error);
      return {
        success: false,
        error: error.message || 'Subscription creation failed',
      };
    }
  }

  async cancelSubscription(subscriptionId: string, immediate: boolean = false): Promise<boolean> {
    try {
      await paymentAPI.cancelSubscription(subscriptionId, immediate);
      return true;
    } catch (error: any) {
      console.error('Subscription cancellation failed:', error);
      return false;
    }
  }

  async getPaymentMethods() {
    try {
      const response = await paymentAPI.getPaymentMethods();
      return response.data.paymentMethods;
    } catch (error: any) {
      console.error('Failed to fetch payment methods:', error);
      return [];
    }
  }

  async addPaymentMethod(paymentData: any) {
    try {
      const response = await paymentAPI.addPaymentMethod(paymentData);
      return response.data;
    } catch (error: any) {
      console.error('Failed to add payment method:', error);
      throw error;
    }
  }

  async removePaymentMethod(paymentMethodId: string) {
    try {
      await paymentAPI.removePaymentMethod(paymentMethodId);
      return true;
    } catch (error: any) {
      console.error('Failed to remove payment method:', error);
      return false;
    }
  }

  // Utility methods
  formatCurrency(amount: number, currency: string = 'USD'): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase(),
    }).format(amount);
  }

  validateAmount(amount: number): boolean {
    return amount > 0 && Number.isFinite(amount);
  }

  validateCurrency(currency: string): boolean {
    const supportedCurrencies = ['USD', 'INR', 'EUR', 'GBP'];
    return supportedCurrencies.includes(currency.toUpperCase());
  }
}

// Create singleton instance
const paymentService = new PaymentService();

export default paymentService;
